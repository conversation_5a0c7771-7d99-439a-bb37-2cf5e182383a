import { IContactResponse } from '@/apis/contact/contact.type';
import { ICustomer, Contacts } from '@/apis/customer/customer.type';
import { CompanyContactRole } from '@/constants/sharedData/sharedData.enums';
import { useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import {
    Button,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
} from 'reactstrap';

interface ContactsTableProps {
    contactList: IContactResponse[];
    toggleAddContactModal: () => void;
    role: CompanyContactRole;
    title: string;
    buttonText: string;
}

const ContactsTable = ({
    contactList,
    toggleAddContactModal,
    role,
    title,
}: ContactsTableProps) => {
    const [contactDropdownOpen, setContactDropdownOpen] = useState<
        number | null
    >(null);

    const { control } = useFormContext<ICustomer>();

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'contacts',
        keyName: 'fieldId',
    });

    const filteredFields = fields.filter(
        (contact: Contacts & { fieldId: string }) => contact.role === role,
    );

    const otherRole =
        role === CompanyContactRole.Purchase
            ? CompanyContactRole.Usage
            : CompanyContactRole.Purchase;

    const otherRoleContacts = fields.filter(
        (contact: Contacts & { fieldId: string }) => contact.role === otherRole,
    );

    const otherRoleContactIds = otherRoleContacts
        .filter(
            (contact: Contacts & { fieldId: string }) =>
                contact.contactId && contact.contactId !== '',
        )
        .map((contact: Contacts & { fieldId: string }) => contact.contactId);

    const toggleContactDropdown = (index: number) => {
        setContactDropdownOpen(contactDropdownOpen === index ? null : index);
    };

    const handleAddContact = () => {
        append({
            contactId: '',
            role: role,
        });
    };

    const handleSelectContact = (index: number, contact: IContactResponse) => {
        const actualIndex = fields.findIndex(
            (f) => f === filteredFields[index],
        );

        if (actualIndex >= 0) {
            const updatedContact = {
                contactId: contact.id,
                role: role,
            };

            remove(actualIndex);
            append(updatedContact, { shouldFocus: false });
        }

        setContactDropdownOpen(null);
    };

    const handleRemoveContact = (index: number) => {
        const actualIndex = fields.findIndex(
            (f) => f === filteredFields[index],
        );

        if (actualIndex >= 0) {
            remove(actualIndex);
        }
    };

    return (
        <div>
            <div className='d-flex justify-content-between align-items-center mb-3'>
                <div className='mb-0' style={{ fontSize: '16px' }}>
                    {title}
                </div>
                <Button
                    size='sm'
                    className='d-flex align-items-center'
                    onClick={handleAddContact}
                    style={{
                        border: '1px solid #60cfbf',
                        backgroundColor: '#ffffff',
                        color: '#60cfbf',
                        fontSize: '13px',
                    }}
                >
                    <i className='ri-add-line me-1'></i> Thêm mới cá nhân
                </Button>
            </div>
            <table
                className='table mb-0'
                style={{
                    tableLayout: 'fixed',
                    width: '100%',
                    borderCollapse: 'collapse',
                }}
            >
                <thead style={{ backgroundColor: '#f3f6f9' }}>
                    <tr>
                        <th style={{ width: '8%' }}>Dòng</th>
                        <th style={{ width: '20%' }}>Họ và tên</th>
                        <th style={{ width: '20%' }}>Phòng ban</th>
                        <th style={{ width: '20%' }}>Chức vụ</th>
                        <th style={{ width: '27%' }}>Vai trò</th>
                        <th style={{ width: '5%' }}></th>
                    </tr>
                </thead>
                <tbody>
                    {filteredFields.map(
                        (field: Contacts & { fieldId: string }, index) => {
                            const contactDetail = contactList.find(
                                (c) => c.id === field.contactId,
                            );

                            return (
                                <tr key={field.fieldId}>
                                    <td
                                        style={{
                                            width: '8%',
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        {index + 1}
                                    </td>
                                    <td
                                        style={{
                                            width: '20%',
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        <div>Chọn cá nhân</div>

                                        <Dropdown
                                            isOpen={
                                                contactDropdownOpen === index
                                            }
                                            toggle={() =>
                                                toggleContactDropdown(index)
                                            }
                                        >
                                            <DropdownToggle
                                                tag='div'
                                                style={{ cursor: 'pointer' }}
                                            >
                                                {contactDetail?.name}
                                            </DropdownToggle>
                                            <DropdownMenu
                                                style={{
                                                    width: '300px',
                                                    maxHeight: '200px',
                                                    overflowY: 'auto',
                                                    position: 'relative',
                                                    top: '20px',
                                                }}
                                            >
                                                {contactList
                                                    .filter((option) => {
                                                        const isInCurrentRole =
                                                            filteredFields.some(
                                                                (
                                                                    f: Contacts & {
                                                                        fieldId: string;
                                                                    },
                                                                ) =>
                                                                    f.contactId ===
                                                                        option.id &&
                                                                    f.contactId !==
                                                                        '',
                                                            );

                                                        const isInOtherRole =
                                                            otherRoleContactIds.includes(
                                                                option.id,
                                                            );

                                                        return (
                                                            !isInCurrentRole &&
                                                            !isInOtherRole
                                                        );
                                                    })
                                                    .map((option, optIndex) => (
                                                        <DropdownItem
                                                            key={optIndex}
                                                            onClick={() =>
                                                                handleSelectContact(
                                                                    index,
                                                                    option,
                                                                )
                                                            }
                                                        >
                                                            <div>
                                                                <strong>
                                                                    {
                                                                        option.name
                                                                    }
                                                                </strong>
                                                            </div>
                                                            <div className='text-muted small'>
                                                                {
                                                                    option.departmentName
                                                                }{' '}
                                                                -{' '}
                                                                {
                                                                    option.positionName
                                                                }{' '}
                                                                -{' '}
                                                                <span className='text-info'>
                                                                    {
                                                                        option.roleName
                                                                    }
                                                                </span>
                                                            </div>
                                                        </DropdownItem>
                                                    ))}
                                                <DropdownItem divider />
                                                <DropdownItem
                                                    onClick={
                                                        toggleAddContactModal
                                                    }
                                                >
                                                    <div
                                                        className='text-primary'
                                                        style={{
                                                            display: 'flex',
                                                            alignContent:
                                                                'center',
                                                        }}
                                                    >
                                                        <i
                                                            className='ri-add-line me-1'
                                                            style={{
                                                                color: '#0AB39C',
                                                            }}
                                                        ></i>
                                                        <div
                                                            style={{
                                                                color: '#0AB39C',
                                                            }}
                                                        >
                                                            Thêm cá nhân
                                                        </div>
                                                    </div>
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </td>
                                    <td
                                        style={{
                                            width: '20%',
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        {contactDetail?.departmentName || ''}
                                    </td>
                                    <td
                                        style={{
                                            width: '20%',
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        {contactDetail?.positionName || ''}
                                    </td>
                                    <td
                                        style={{
                                            width: '27%',
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        {contactDetail?.roleName || ''}
                                    </td>
                                    <td
                                        style={{
                                            display: 'flex',
                                            justifyContent: 'center',
                                            verticalAlign: 'middle',
                                        }}
                                    >
                                        <button
                                            type='button'
                                            style={{
                                                color: '#e74c3c',
                                                fontSize: '20px',
                                            }}
                                            onClick={() =>
                                                handleRemoveContact(index)
                                            }
                                        >
                                            <i className='ri-delete-bin-line'></i>
                                        </button>
                                    </td>
                                </tr>
                            );
                        },
                    )}
                    {filteredFields.length === 0 && (
                        <tr>
                            <td
                                colSpan={6}
                                className='text-center py-3'
                                style={{ verticalAlign: 'middle' }}
                            >
                                Chưa có dữ liệu. Vui lòng thêm cá nhân.
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );
};

export default ContactsTable;
